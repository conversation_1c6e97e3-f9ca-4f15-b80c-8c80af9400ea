# SeQRNG-CTM REST API Configuration
# Copy this file to .env and update with your actual values

# SeQRNG Configuration
# The IP address or URL of your SeQRNG server
SEQRNG_IP_ADDRESS=http://your-seqrng-server:1982

# Your SeQRNG API token for authentication
SEQRNG_API_TOKEN=your-seqrng-api-token

# CTM (CipherTrust Manager) Configuration
# The IP address or hostname of your CTM server
CTM_IP_ADDRESS=your-ctm-server

# CTM authentication credentials
CTM_USERNAME=your-ctm-username
CTM_PASSWORD=your-ctm-password
CTM_DOMAIN=your-ctm-domain

# Flask Application Configuration
# Port for the API server (default: 3001)
PORT=3001

# Flask debug mode (true/false, default: false for production)
FLASK_DEBUG=false

# Flask host binding (default: 0.0.0.0 for all interfaces)
FLASK_HOST=0.0.0.0

# Additional Configuration (Optional)
# Uncomment and modify as needed

# FLASK_ENV=production
# LOG_LEVEL=INFO
