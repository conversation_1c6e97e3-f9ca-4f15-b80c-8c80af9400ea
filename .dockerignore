# Environment files - Include .env but exclude sensitive variants
# .env - This will be included for configuration
.env.local
.env.production
.env.staging
.env.development
.env.test

# Configuration files with credentials
config.json
secrets.json

# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
/tmp/

# Backup files
*.bak
*.backup

# Test files
tests/
test_*.py
*_test.py

# Documentation (if you don't want to include it)
docs/
*.md
README*

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Conda
.conda/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
